#!/bin/bash

# 脱困检测分析脚本使用示例

# 设置默认参数
LOG_FILE="/userdata/log/navigation_mower_node.log"
OUTPUT_DIR="/userdata/log/analysis"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="${OUTPUT_DIR}/stuck_detection_analysis_${TIMESTAMP}.png"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo "错误: 日志文件不存在: $LOG_FILE"
    echo "请指定正确的日志文件路径"
    echo "用法: $0 [日志文件路径]"
    exit 1
fi

# 如果提供了参数，使用参数作为日志文件路径
if [ $# -gt 0 ]; then
    LOG_FILE="$1"
fi

echo "开始分析脱困检测数据..."
echo "日志文件: $LOG_FILE"
echo "输出文件: $OUTPUT_FILE"

# 运行Python分析脚本
python3 "$(dirname "$0")/analyze_stuck_detection.py" "$LOG_FILE" -o "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo "分析完成! 结果已保存到: $OUTPUT_FILE"
    
    # 如果是在有图形界面的环境中，可以尝试打开图片
    if command -v xdg-open &> /dev/null; then
        echo "尝试打开图片..."
        xdg-open "$OUTPUT_FILE" 2>/dev/null &
    fi
else
    echo "分析失败!"
    exit 1
fi
