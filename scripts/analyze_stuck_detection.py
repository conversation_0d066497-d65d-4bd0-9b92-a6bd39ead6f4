#!/usr/bin/env python3
"""
脱困检测角度累计量分析脚本

用于分析日志文件中的角度累计量数据，绘制5分钟、10分钟、15分钟窗口的角度累计量图表
"""

import re
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import argparse
import sys
import os

class StuckDetectionAnalyzer:
    def __init__(self):
        self.data = {
            '5min': {'timestamps': [], 'angles': [], 'threshold': 2.09, 'stuck_status': []},
            '10min': {'timestamps': [], 'angles': [], 'threshold': 4.19, 'stuck_status': []},
            '15min': {'timestamps': [], 'angles': [], 'threshold': 6.28, 'stuck_status': []}
        }
        
    def parse_log_line(self, line):
        """解析日志行，提取角度累计量数据"""
        # 匹配日志格式：[2025-05-31 14:35:24.725] [navigation_mower_node] [warning] stuck_detection_recovery.cpp[697] [StuckDetectionRecovery] 5分钟窗口: 角度累计=67.210rad, 阈值=2.090rad, 被困=否, 数据=充足
        pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*\[StuckDetectionRecovery\] (\d+)分钟窗口: 角度累计=([\d.]+)rad, 阈值=([\d.]+)rad, 被困=([是否]), 数据=([充足不足]+)'
        
        match = re.search(pattern, line)
        if match:
            timestamp_str = match.group(1)
            window_minutes = match.group(2)
            angle_cumulative = float(match.group(3))
            threshold = float(match.group(4))
            stuck_status = match.group(5) == '是'
            data_status = match.group(6)
            
            # 转换时间戳
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            
            return {
                'timestamp': timestamp,
                'window': f'{window_minutes}min',
                'angle': angle_cumulative,
                'threshold': threshold,
                'stuck': stuck_status,
                'data_sufficient': data_status == '充足'
            }
        return None
    
    def parse_log_file(self, log_file_path):
        """解析日志文件"""
        print(f"正在解析日志文件: {log_file_path}")
        
        if not os.path.exists(log_file_path):
            print(f"错误: 日志文件不存在: {log_file_path}")
            return False
            
        line_count = 0
        parsed_count = 0
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line_count += 1
                    parsed_data = self.parse_log_line(line)
                    if parsed_data:
                        parsed_count += 1
                        window = parsed_data['window']
                        if window in self.data:
                            self.data[window]['timestamps'].append(parsed_data['timestamp'])
                            self.data[window]['angles'].append(parsed_data['angle'])
                            self.data[window]['stuck_status'].append(parsed_data['stuck'])
                            
        except Exception as e:
            print(f"错误: 读取日志文件失败: {e}")
            return False
            
        print(f"总共处理 {line_count} 行，解析出 {parsed_count} 条有效数据")
        
        # 打印每个窗口的数据统计
        for window, data in self.data.items():
            print(f"{window}窗口: {len(data['timestamps'])} 条数据")
            
        return parsed_count > 0
    
    def plot_analysis(self, output_path=None):
        """绘制分析图表"""
        if not any(len(data['timestamps']) > 0 for data in self.data.values()):
            print("错误: 没有有效数据可以绘制")
            return False
            
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建子图
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle('脱困检测角度累计量分析', fontsize=16, fontweight='bold')
        
        colors = ['blue', 'green', 'red']
        windows = ['5min', '10min', '15min']
        
        for i, window in enumerate(windows):
            ax = axes[i]
            data = self.data[window]
            
            if len(data['timestamps']) == 0:
                ax.text(0.5, 0.5, f'{window}窗口: 无数据', 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{window}窗口 (无数据)')
                continue
                
            # 绘制角度累计量
            ax.plot(data['timestamps'], data['angles'], 
                   color=colors[i], linewidth=2, label=f'{window}窗口角度累计量')
            
            # 绘制阈值线
            threshold = data['threshold']
            ax.axhline(y=threshold, color='red', linestyle='--', 
                      linewidth=2, label=f'阈值 ({threshold:.2f} rad)')
            
            # 标记被困状态
            stuck_times = [t for t, stuck in zip(data['timestamps'], data['stuck_status']) if stuck]
            if stuck_times:
                stuck_angles = [a for a, stuck in zip(data['angles'], data['stuck_status']) if stuck]
                ax.scatter(stuck_times, stuck_angles, color='red', s=50, 
                          marker='x', label='被困状态', zorder=5)
            
            # 设置图表属性
            ax.set_title(f'{window}窗口角度累计量 (阈值: {threshold:.2f} rad)')
            ax.set_ylabel('角度累计量 (rad)')
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # 格式化x轴时间
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 添加统计信息
            min_angle = min(data['angles']) if data['angles'] else 0
            max_angle = max(data['angles']) if data['angles'] else 0
            avg_angle = sum(data['angles']) / len(data['angles']) if data['angles'] else 0
            stuck_count = sum(data['stuck_status'])
            
            stats_text = f'最小值: {min_angle:.2f} rad\n最大值: {max_angle:.2f} rad\n平均值: {avg_angle:.2f} rad\n被困次数: {stuck_count}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存或显示图表
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {output_path}")
        else:
            plt.show()
            
        return True

def main():
    parser = argparse.ArgumentParser(description='分析脱困检测角度累计量数据')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('-o', '--output', help='输出图片文件路径 (可选，不指定则显示图表)')
    
    args = parser.parse_args()
    
    analyzer = StuckDetectionAnalyzer()
    
    # 解析日志文件
    if not analyzer.parse_log_file(args.log_file):
        print("解析日志文件失败")
        sys.exit(1)
    
    # 绘制分析图表
    if not analyzer.plot_analysis(args.output):
        print("绘制图表失败")
        sys.exit(1)
    
    print("分析完成!")

if __name__ == '__main__':
    main()
