#!/usr/bin/env python3
"""
测试脱困检测分析脚本的功能
生成模拟日志数据并测试分析功能
"""

import os
import sys
from datetime import datetime, timedelta
import random

def generate_test_log(filename, duration_minutes=30):
    """生成测试日志文件"""
    print(f"生成测试日志文件: {filename}")
    
    start_time = datetime.now() - timedelta(minutes=duration_minutes)
    
    with open(filename, 'w', encoding='utf-8') as f:
        current_time = start_time
        
        # 模拟不同阶段的角度累计量
        for minute in range(duration_minutes):
            for second in range(0, 60, 10):  # 每10秒一条记录
                timestamp = current_time + timedelta(minutes=minute, seconds=second)
                timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                
                # 模拟角度累计量变化
                base_angle_5min = minute * 0.5 + random.uniform(-0.2, 0.2)
                base_angle_10min = minute * 1.0 + random.uniform(-0.4, 0.4)
                base_angle_15min = minute * 1.5 + random.uniform(-0.6, 0.6)
                
                # 在某些时间段模拟被困状态
                stuck_5min = "是" if base_angle_5min < 2.0 and minute > 10 else "否"
                stuck_10min = "是" if base_angle_10min < 4.0 and minute > 15 else "否"
                stuck_15min = "是" if base_angle_15min < 6.0 and minute > 20 else "否"
                
                # 写入5分钟窗口数据
                f.write(f"[{timestamp_str}] [navigation_mower_node] [warning] stuck_detection_recovery.cpp[697] "
                       f"[StuckDetectionRecovery] 5分钟窗口: 角度累计={base_angle_5min:.3f}rad, "
                       f"阈值=2.090rad, 被困={stuck_5min}, 数据=充足\n")
                
                # 写入10分钟窗口数据
                f.write(f"[{timestamp_str}] [navigation_mower_node] [warning] stuck_detection_recovery.cpp[697] "
                       f"[StuckDetectionRecovery] 10分钟窗口: 角度累计={base_angle_10min:.3f}rad, "
                       f"阈值=4.190rad, 被困={stuck_10min}, 数据=充足\n")
                
                # 写入15分钟窗口数据
                f.write(f"[{timestamp_str}] [navigation_mower_node] [warning] stuck_detection_recovery.cpp[697] "
                       f"[StuckDetectionRecovery] 15分钟窗口: 角度累计={base_angle_15min:.3f}rad, "
                       f"阈值=6.280rad, 被困={stuck_15min}, 数据=充足\n")
                
                # 添加一些其他日志行（应该被忽略）
                f.write(f"[{timestamp_str}] [navigation_mower_node] [info] 其他日志信息\n")
    
    print(f"测试日志文件生成完成，包含 {duration_minutes * 6 * 3} 条脱困检测记录")

def test_analysis():
    """测试分析功能"""
    # 生成测试日志
    test_log_file = "/tmp/test_stuck_detection.log"
    test_output_file = "/tmp/test_stuck_analysis.png"
    
    generate_test_log(test_log_file, 30)
    
    # 导入分析模块
    sys.path.append(os.path.dirname(__file__))
    from analyze_stuck_detection import StuckDetectionAnalyzer
    
    # 创建分析器并解析日志
    analyzer = StuckDetectionAnalyzer()
    
    print("开始解析测试日志...")
    if not analyzer.parse_log_file(test_log_file):
        print("解析失败!")
        return False
    
    print("开始生成分析图表...")
    if not analyzer.plot_analysis(test_output_file):
        print("生成图表失败!")
        return False
    
    print(f"测试完成! 分析结果已保存到: {test_output_file}")
    
    # 打印统计信息
    for window, data in analyzer.data.items():
        if data['timestamps']:
            print(f"{window}窗口统计:")
            print(f"  数据点数: {len(data['timestamps'])}")
            print(f"  角度范围: {min(data['angles']):.3f} - {max(data['angles']):.3f} rad")
            print(f"  被困次数: {sum(data['stuck_status'])}")
            print(f"  阈值: {data['threshold']:.3f} rad")
    
    # 清理测试文件
    try:
        os.remove(test_log_file)
        print(f"已清理测试日志文件: {test_log_file}")
    except:
        pass
    
    return True

if __name__ == '__main__':
    print("开始测试脱困检测分析脚本...")
    
    if test_analysis():
        print("测试成功!")
        sys.exit(0)
    else:
        print("测试失败!")
        sys.exit(1)
