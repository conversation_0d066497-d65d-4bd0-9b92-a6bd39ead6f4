#ifndef SLIDING_WINDOW_HPP
#define SLIDING_WINDOW_HPP

#include <cstdint>
#include <deque>

namespace fescue_iox
{

// 运动数据结构
struct MovementData
{
    uint64_t timestamp;         // 时间戳 (ms)
    float angular_displacement; // 角度位移 (rad) - 这一帧的角度变化
    float angular_velocity;     // 角速度 (rad/s)

    MovementData()
        : timestamp(0)
        , angular_displacement(0.0f)
        , angular_velocity(0.0f)
    {
    }
};

// 滑动窗口管理结构
struct SlidingWindow
{
    uint64_t window_duration_ms_;   // 窗口持续时间
    float rotation_threshold_;      // 旋转阈值
    std::deque<MovementData> data_; // 窗口内的数据
    float total_rotation_;          // 窗口内总旋转量
    uint64_t last_update_time_;     // 上次更新时间

    SlidingWindow(uint64_t duration_ms, float threshold);

    // 添加新数据并更新窗口
    void AddData(const MovementData &new_data);

    // 清理过期数据
    void CleanupExpiredData(uint64_t current_time);

    // 检查是否被困
    bool IsStuck() const;

    // 检查数据是否充足
    bool HasSufficientData(uint64_t current_time) const;

    // 重置窗口
    void Reset();
};

} // namespace fescue_iox

#endif // SLIDING_WINDOW_HPP
