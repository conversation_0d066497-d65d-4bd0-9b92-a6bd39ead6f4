#include "sliding_window.hpp"
#include <cmath>

namespace fescue_iox
{

SlidingWindow::SlidingWindow(uint64_t duration_ms, float threshold)
    : window_duration_ms_(duration_ms)
    , rotation_threshold_(threshold)
    , total_rotation_(0.0f)
    , last_update_time_(0)
{
}

void SlidingWindow::AddData(const MovementData &new_data)
{
    // 添加新数据
    data_.push_back(new_data);
    total_rotation_ += std::abs(new_data.angular_displacement);
    last_update_time_ = new_data.timestamp;

    // 清理过期数据
    CleanupExpiredData(new_data.timestamp);
}

void SlidingWindow::CleanupExpiredData(uint64_t current_time)
{
    uint64_t cutoff_time = current_time - window_duration_ms_;

    // 移除过期数据并更新总旋转量
    while (!data_.empty() && data_.front().timestamp < cutoff_time)
    {
        total_rotation_ -= std::abs(data_.front().angular_displacement);
        data_.pop_front();
    }

    // 确保总旋转量不为负数（防止浮点精度问题）
    if (total_rotation_ < 0.0f)
    {
        total_rotation_ = 0.0f;
    }
}

bool SlidingWindow::IsStuck() const
{
    return total_rotation_ < rotation_threshold_;
}

bool SlidingWindow::HasSufficientData(uint64_t current_time) const
{
    if (data_.empty())
    {
        return false;
    }

    // 检查数据覆盖时间是否足够（至少80%的窗口时间）
    uint64_t earliest_timestamp = data_.front().timestamp;
    uint64_t data_coverage_duration = current_time - earliest_timestamp;
    uint64_t min_coverage_duration = window_duration_ms_ * 0.8;

    return data_coverage_duration >= min_coverage_duration && data_.size() >= 10;
}

void SlidingWindow::Reset()
{
    data_.clear();
    total_rotation_ = 0.0f;
    last_update_time_ = 0;
}

} // namespace fescue_iox
