#include "mower_node.hpp"

#include "mower_node_config.hpp"
#include "mower_sdk_version.h"
#include "nav_common.hpp"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationMowerNode::NavigationMowerNode(const std::string &node_name)
    : node_name_(node_name)

{
    last_mcu_exception_time_ = std::chrono::steady_clock::now();
    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
    stuck_detection_state_change_time_ = std::chrono::steady_clock::now();

    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitHeartbeat();
    InitAlgorithm();
    InitTaskStateByMCUMissionType();

#if PRINTF_IMU_DATA
    OpenImuDataFile();
#endif
}

NavigationMowerNode::~NavigationMowerNode()
{
    DeinitAlgorithm();

#if PRINTF_IMU_DATA
    if (imu_data_file_.is_open())
    {
        imu_data_file_.close();
        LOG_INFO("IMU数据文件已关闭");
    }
#endif

    last_mcu_exception_time_ = std::chrono::steady_clock::now();
    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
    LOG_WARN("NavigationMowerNode exit!");
}

void NavigationMowerNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationMowerNode::InitParam()
{
    const std::string conf_file{"conf/navigation_mower_node/navigation_mower_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationMowerNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationMowerNode create config path failed!!!");
        }
    }
    if (!Config<NavigationMowerNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationMowerNode config parameters failed!");
    }
    NavigationMowerNodeConfig config = Config<NavigationMowerNodeConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    chassis_type_ = config.chassis_type;
    mower_alg_conf_file_ = config.mower_alg_conf_file;

    if (!Config<NavigationMowerNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationMowerNodeConfig config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationMowerNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationMowerNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationMowerNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(mower_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Mower algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Mower algo create config path failed!!!");
        }
    }
    if (!Config<NavigationMowerAlgConfig>::Init(mower_alg_conf_file_))
    {
        LOG_WARN("Init Mower algo config parameters failed!");
    }
    NavigationMowerAlgConfig config = Config<NavigationMowerAlgConfig>::GetConfig();

    LOG_INFO("[navigation_mower_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_mower_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_mower_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());
    ConfigParamToAlgorithmParam(config, mower_alg_param_);
    if (!Config<NavigationMowerAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Mower algo config parameters failed!");
    }
}

void NavigationMowerNode::InitAlgorithm()
{
    InitAlgorithmParam();

    mower_alg_ = std::make_unique<NavigationMowerAlg>(mower_alg_param_);

    mower_alg_->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData> &data) -> void {
        this->DealFeatureSelectCallback(data);
    });
    mower_alg_->SetMarkLocationMarkIdCallback([this](int mark_id) -> bool {
        return this->DealMarkLocationMarkIdCallback(mark_id);
    });
    mower_alg_->SetCrossRegionRunningStateCallback([this](CrossRegionRunningState state) -> void {
        this->DealCrossRegionRunningStateCallback(state);
    });

    mower_alg_->SetRechargeRunningStateCallback([this](RechargeRunningState state) -> void {
        this->DealRechargeRunningStateCallback(state);
    });

    mower_alg_->SetUndockResultCallback([this](bool completed, bool result, mower_msgs::srv::UndockOperationStatus status) -> void {
        this->DealUndockFinalResult(completed, result, status);
    });

    mower_alg_->SetAreaCalcStartCallback([this](uint64_t timestamp) -> bool {
        return this->DealRegionExploreAreaCalcStart(timestamp);
    });

    mower_alg_->SetAreaCalcStopCallback([this](uint64_t timestamp, float &area, float &perimeter) -> bool {
        return this->DealRegionExploreAreaCalcStop(timestamp, area, perimeter);
    });

    mower_alg_->SetRegionExploreResultCallback([this](RegionExploreResult &result) -> void {
        this->DealRegionExploreResult(result);
    });

    mower_alg_->SetCutBorderResultCallback([this](bool completed, bool result) -> void {
        this->DealCutBorderResult(completed, result);
    });

    thread_running_.store(true);
    mower_thread_ = std::thread(&NavigationMowerNode::MowerThread, this);
}

void NavigationMowerNode::DeinitAlgorithm()
{
    if (mower_alg_)
    {
        mower_alg_->ProhibitVelPublisher();
    }

    thread_running_.store(false);
    if (mower_thread_.joinable())
    {
        mower_thread_.join();
    }
}

void NavigationMowerNode::InitSubscriber()
{
    sub_mark_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>>(
        "mark_location_result", 1, [this](const fescue_msgs__msg__MarkLocationResult &data, const std::string &event) {
            DealMarkLocationResult(data);
        });

    sub_cross_region_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>>(
        "navigation_cross_region_state", 1, [this](const fescue_msgs__msg__CrossRegionStateData &data, const std::string &event) {
            DealCrossRegionState(data);
        });

    sub_recharge_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__RechargeStateData>>(
        "navigation_recharge_state", 1, [this](const fescue_msgs__msg__RechargeStateData &data, const std::string &event) {
            DealRechargeState(data);
        });

    sub_behavior_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__BehaviorStateData>>(
        "navigation_behavior_state", 1, [this](const fescue_msgs__msg__BehaviorStateData &data, const std::string &event) {
            DealBehaviorState(data);
        });

    sub_random_mower_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__RandomMowerStateData>>(
        "navigation_random_mower_state", 1, [this](const fescue_msgs__msg__RandomMowerStateData &data, const std::string &event) {
            DealRandomMowerState(data);
        });

    sub_nav_twist_ = std::make_unique<IceoryxSubscriberMower<geometry_msgs__msg__Twist_iox>>(
        "navigation_mower_twist", 1, [this](const geometry_msgs__msg__Twist_iox &data, const std::string &event) {
            DealMowerTwist(data);
        });

    sub_perception_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            DealPerceptionFusionResult(data);
        });

    sub_qrcode_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>>(
        "detect_qrcode_result", 1, [this](const fescue_msgs__msg__QrCodeResult &data, const std::string &event) {
            DealQRCodeLocationResult(data);
        });

    sub_charge_pile_dock_status_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::ChargePileDockStatus>>(
        "mcu_charge_pile_dock_status", 1, [this](const mower_msgs::msg::ChargePileDockStatus &data, const std::string &event) {
            DealChargePileDockStatus(data);
        });

    sub_recharge_final_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavRechargeFinalResult>>(
        "navigation_recharge_final_result", 1, [this](const fescue_msgs__msg__NavRechargeFinalResult &data, const std::string &event) {
            DealRechargeFinalResult(data);
        });

    sub_cross_region_final_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavCrossRegionFinalResult>>(
        "navigation_cross_region_final_result", 1, [this](const fescue_msgs__msg__NavCrossRegionFinalResult &data, const std::string &event) {
            DealCrossRegionFinalResult(data);
        });

    sub_spiral_mower_final_result_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::NavSpiralMowerFinalResult>>(
        "navigation_spiral_mower_final_result", 1, [this](const ob_mower_msgs::NavSpiralMowerFinalResult &data, const std::string &event) {
            DealSpiralMowerFinalResult(data);
        });

    sub_mcu_sensor_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>>(
        "mcu_sensor", 1, [this](const mower_msgs::msg::McuSensor &data, const std::string &event) {
            DealMCUSensor(data);
        });

    sub_mcu_exception_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuException>>(
        "mcu_exception", 1, [this](const mower_msgs::msg::McuException &data, const std::string &event) {
            DealMCUException(data);
        });

    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            DealMcuImu(data);
        });

    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            DealMcuMotorSpeed(data);
        });

    sub_motion_detection_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__LocalizationMotionDetectionResult>>(
        "localization_motion_detection_result", 1, [this](const fescue_msgs__msg__LocalizationMotionDetectionResult &data, const std::string &event) {
            DealMotionDetectionResult(data);
        });
}

void NavigationMowerNode::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_nav_alg_ctrl_ = std::make_unique<iox_nav_alg_ctrl_publisher>(
        iox::capro::ServiceDescription{kNavigationNavAlgCtrlIox[0],
                                       kNavigationNavAlgCtrlIox[1],
                                       kNavigationNavAlgCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    // 发布 cross region 运行状态
    pub_cross_region_state_ = std::make_unique<iox_cross_region_state_publisher>(
        iox::capro::ServiceDescription{kNavigationCrossRegionStateIox[0],
                                       kNavigationCrossRegionStateIox[1],
                                       kNavigationCrossRegionStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    // 发布 running state
    pub_nav_running_state_ = std::make_unique<iox_nav_running_state_publisher>(
        iox::capro::ServiceDescription{kNavigationRunningStateIox[0],
                                       kNavigationRunningStateIox[1],
                                       kNavigationRunningStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_recharge_state_ = std::make_unique<iox_recharge_state_publisher>(
        iox::capro::ServiceDescription{kNavigationRechargeStateIox[0],
                                       kNavigationRechargeStateIox[1],
                                       kNavigationRechargeStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_nav_twist_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::Twist>>("mcu_twist");

    pub_perception_localization_alg_ctrl_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl");
}

void NavigationMowerNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_mower_node_param_request", 10,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            response.success = GetMowerNodeParam(response.data);
            LOG_INFO("Get navigtation mower node param request execute {}!", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_mower_node_param_request", 10,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetMowerNodeParam(request.data);
            LOG_INFO("Set navigtation mower node param request execute {}!", response.success);
        });
    service_sw_go_mower_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoMowRequest, mower_msgs::srv::GoMowResponse>>(
        "go_to_mow", 10,
        [this](const mower_msgs::srv::GoMowRequest &request, mower_msgs::srv::GoMowResponse &response) {
            response.success = DealSWMowerRequest(request.request_type);
            LOG_INFO("Set navigtation mower go mower request execute {}!", response.success);
        });
    service_sw_go_charge_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoChargeRequest, mower_msgs::srv::GoChargeResponse>>(
        "go_to_charge", 10,
        [this](const mower_msgs::srv::GoChargeRequest &request, mower_msgs::srv::GoChargeResponse &response) {
            response.success = DealSWChargeRequest(request.request_type);
            LOG_INFO("Set navigtation mower go charge request execute {}!", response.success);
        });
    service_sw_go_cross_region_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToCrossRegionRequest, mower_msgs::srv::GoToCrossRegionResponse>>(
        "go_to_cross_region", 10,
        [this](const mower_msgs::srv::GoToCrossRegionRequest &request, mower_msgs::srv::GoToCrossRegionResponse &response) {
            response.success = DealSWGoCrossRegionRequest(request.request_type);
            LOG_INFO("Set navigtation mower go cross region request execute {}!", response.success);
        });
    service_sw_go_standby_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToStandbyRequest, mower_msgs::srv::GoToStandbyResponse>>(
        "go_to_standby", 10,
        [this](const mower_msgs::srv::GoToStandbyRequest &request, mower_msgs::srv::GoToStandbyResponse &response) {
            response.success = DealSWStandByRequest(request.request_type);
            LOG_INFO("Set navigtation mower go standby request execute {}!", response.success);
        });
    service_sw_explore_map_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::ExploreMapRequest, mower_msgs::srv::ExploreMapResponse>>(
        "explore_map", 10,
        [this](const mower_msgs::srv::ExploreMapRequest &request, mower_msgs::srv::ExploreMapResponse &response) {
            response.success = DealSWRegionExploreRequest(request.request_type);
            LOG_INFO("Set navigaiton mower region explore request execute {}!", response.success);
        });
    service_sw_go_cut_edge_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToCutEdgeRequest, mower_msgs::srv::GoToCutEdgeResponse>>(
        "go_to_cut_edge", 10,
        [this](const mower_msgs::srv::GoToCutEdgeRequest &request, mower_msgs::srv::GoToCutEdgeResponse &response) {
            response.success = DealSWCutBorderRequest(request.request_type);
            LOG_INFO("Set navigaiton mower cut edge request execute {}!", response.success);
        });
}

void NavigationMowerNode::InitTaskStateByMCUMissionType()
{
    mower_msgs::srv::MowerMissionType mission_type{mower_msgs::srv::MowerMissionType::UNKNOWN};
    if (GetMCUMissionType(mission_type))
    {
        LOG_INFO("Current MCU mission type is {}", mower_msgs::srv::asStringLiteral(mission_type).c_str());
        if (mission_type == mower_msgs::srv::MowerMissionType::POWER_SAVING_STANDBY)
        {
            is_recharge_start_ = false;
            is_cross_region_start_ = false;
            is_random_mower_start_ = false;
            is_region_explore_start_ = false;
            is_spiral_mower_start_ = false;
            is_cut_border_start_ = false;
            CloseAllTask();
            SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::DISABLE);
        }
    }
}

void NavigationMowerNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    std::lock_guard<std::mutex> lock(fusion_mutex_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;

    if (mower_alg_)
    {
        mower_alg_->SetGrassDetecteStatus(fusion_result_.grass_detecte_status);
    }
}

void NavigationMowerNode::DealMCUException(const mower_msgs::msg::McuException &data)
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    last_mcu_exception_time_ = std::chrono::steady_clock::now();

    if (data.exception_value != mower_msgs::msg::McuExceptionValue::NO_EXCEPTION)
    {
        LOG_INFO("[DealMCUException1] {}", mower_msgs::msg::asStringLiteral(data.exception_value).c_str());
    }

    switch (data.exception_value)
    {
    case mower_msgs::msg::McuExceptionValue::COLLISION_BELOW_3_SECOND_EXCEPTION: // collision below 3 seconds 动作：后退，转向继续行进
    {
        LOG_WARN("[DealMCUException1] collision below 3 seconds 动作：后退，转向继续行进");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::COLLISION_ABOVE_3_SECOND_EXCEPTION: // collision above 3 seconds 动作：行进电机后退，并转向执行避障动作
    {
        LOG_WARN("[DealMCUException1] collision above 3 seconds 动作：行进电机后退，并转向执行避障动作");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_0_3_TO_3_SECONDS_EXCEPTION: // single lift sensor trigger 0.3-3 seconds 动作：行走电机继续保持原状态
    {
        LOG_WARN("[DealMCUException1] single lift sensor trigger 0.3-3 seconds 动作：行走电机继续保持原状态");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_3_TO_10_SECONDS_EXCEPTION: // single lift sensor trigger 3-10 seconds 动作：停止割草电机，行进电机后退尝试解除提升
    {
        LOG_WARN("[DealMCUException1] single lift sensor trigger 3-10 seconds 动作：停止割草电机，行进电机后退尝试解除提升");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::DOUBLE_LIFT_SENSOR_TRIGGER_BELOW_10_SECONDS_EXCEPTION: // double lift sensor trigger below 10 seconds 动作：停止割草电机，行进电机后退尝试解除提升
    {
        LOG_WARN("[DealMCUException1] double lift sensor trigger below 10 seconds 动作：停止割草电机，行进电机后退尝试解除提升");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::NO_EXCEPTION:
    {
        LOG_DEBUG("[DealMCUException1] no exception");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    default:
        LOG_DEBUG("[DealMCUException1] No safety abnormalities");
        mcu_exception_status_ = McuExceptionStatus::UNKNOWN;
        break;
    }
}

void NavigationMowerNode::DealMcuImu(const mower_msgs::msg::McuImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(data.angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if PRINTF_IMU_DATA
        // 记录滤波后的IMU数据到文件
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << ","
                           << filter_state_.accel_x << ","
                           << filter_state_.accel_y << ","
                           << filter_state_.accel_z << ","
                           << filter_state_.gyro_x << ","
                           << filter_state_.gyro_y << ","
                           << filter_state_.gyro_z << std::endl;
        }
#endif

        SetImuData(imu_data_);
    }
    else
    {
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = data.angular_velocity_z;
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
        // update IMU datas

#if PRINTF_IMU_DATA
        // 对于未开启滤波的情况，填充滤波数据列为原始数据
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << std::endl;
        }
#endif

        SetImuData(imu_data_);
    }
}

void NavigationMowerNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);

    if (enable_motor_speed_filter_)
    {
        filter_state_.motor_speed_left = LowPassFilter(data.motor_speed_left, filter_state_.motor_speed_left, alpha_speed_);
        filter_state_.motor_speed_right = LowPassFilter(data.motor_speed_right, filter_state_.motor_speed_right, alpha_speed_);
        motor_speed_data_.motor_speed_left = filter_state_.motor_speed_left;
        motor_speed_data_.motor_speed_right = filter_state_.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // motor_speed_data_.current_left = data.current_left; // 电流
        // motor_speed_data_.current_right = data.current_right;

        SetMotorSpeedData(motor_speed_data_);
    }
    else
    {
        motor_speed_data_.motor_speed_left = data.motor_speed_left;
        motor_speed_data_.motor_speed_right = data.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // update encoder datas

        SetMotorSpeedData(motor_speed_data_);
    }
}

void NavigationMowerNode::DealMotionDetectionResult(const fescue_msgs__msg__LocalizationMotionDetectionResult &data)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_.ave_pix_diff = data.ave_pix_diff;
    motion_detection_result_.is_motion = data.is_motion;
    motion_detection_result_.timestamp = data.timestamp_ms;
    // LOG_ERROR("[DealMotionDetectionResult] ave_pix_diff: {}, is_motion: {}", data.ave_pix_diff, data.is_motion);
    // LOG_ERROR("[DealMotionDetectionResult] timestamp: {}", data.timestamp_ms);

    SetMotionDetectionResult(motion_detection_result_);
}

void NavigationMowerNode::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    if (mower_alg_)
    {
        mower_alg_->SetMotorSpeedData(motor_speed_data);
    }
}

void NavigationMowerNode::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    if (mower_alg_)
    {
        mower_alg_->SetMotionDetectionResult(motion_detection_result);
    }
}

void NavigationMowerNode::SetImuData(const ImuData &imu_data)
{
    if (mower_alg_)
    {
        mower_alg_->SetImuData(imu_data);
    }
}

float NavigationMowerNode::LowPassFilter(float new_value, float prev_value, float alpha)
{
    return alpha * new_value + (1.0f - alpha) * prev_value;
}

void NavigationMowerNode::DealMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    LOG_DEBUG("charge_terminal_status is: {}", data.charge_terminal_status);
    if (mower_alg_)
    {
        mower_alg_->SetMCUSensor(data);
    }
}

void NavigationMowerNode::DealChargePileDockStatus(const mower_msgs::msg::ChargePileDockStatus &msg)
{
    if (mower_alg_)
    {
        mower_alg_->SetChargePileDockStatus(msg.charge_pile_dock_status);
    }
    if (is_recharge_start_)
    {
        if (msg.charge_pile_dock_status == 1)
        {
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(true);
            PublishRechargeAlgCtrl("NavigationMowerNode", false);
        }
    }
}

void NavigationMowerNode::DealMowerTwist(const geometry_msgs__msg__Twist_iox &msg)
{
    std::string sender = std::string(msg.sender.c_str());
    float linear_velocity = msg.linear.x;
    float angular_velocity = msg.angular.z;

    // 使用新的脱困检测状态管理逻辑
    // 增加时间阈值，避免频繁开启/关闭脱困检测
    if (mower_alg_)
    {
        UpdateStuckDetectionState(linear_velocity, angular_velocity);
    }

    PublishMowerTwist(sender, linear_velocity, angular_velocity);
}

void NavigationMowerNode::DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg)
{
    cross_region_state_.store(static_cast<CrossRegionRunningState>(msg.state));
    LOG_INFO_THROTTLE(2000, "CrossRegion state is: {}", asStringLiteral(cross_region_state_.load()));
}

void NavigationMowerNode::DealRechargeState(const fescue_msgs__msg__RechargeStateData &msg)
{
    recharge_state_.store(static_cast<RechargeRunningState>(msg.state));
    RechargeRunningState recharge_state = recharge_state_.load();

    LOG_INFO_THROTTLE(2000, "Recharge state is: {}", asStringLiteral(recharge_state));

    if (recharge_state == RechargeRunningState::PER_FOUND_QR_CODE ||
        recharge_state == RechargeRunningState::PROCESS_FOUND_QR_CODE)
    {
        if (temp_recharge_state_.load() != recharge_state)
        {
            temp_recharge_state_.store(recharge_state);
            SetPerceptionAndLocalizationStateOnRechargeHaveFindQRCode();
            SendRechargeFinalResult(false, false, mower_msgs::srv::RechargeOperationStatus::NOT_INTERRUPTIBLE);
        }
    }
    else if (recharge_state == RechargeRunningState::EDGE_FINDING_QR_CODE)
    {
        if (temp_recharge_state_.load() != recharge_state)
        {
            temp_recharge_state_.store(recharge_state);
            SetPerceptionAndLocalizationStateOnRechargeFindingQRCode();
            SendRechargeFinalResult(false, false, mower_msgs::srv::RechargeOperationStatus::INTERRUPTIBLE);
        }
    }
    else
    {
        temp_recharge_state_.store(recharge_state);
    }
}

void NavigationMowerNode::DealBehaviorState(const fescue_msgs__msg__BehaviorStateData &msg)
{
    std::lock_guard<std::mutex> lck(behavior_mtx_);
    behavior_state_ = static_cast<BehaviorRunningState>(msg.state);

    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
}

void NavigationMowerNode::DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg)
{
    // LOG_DEBUG("QRLocation: perception_status {} status {} pose({} {} {}) rpw({} {} {})",
    //           msg.mark_perception_status, msg.status,
    //           msg.pose.position.x, msg.pose.position.y, msg.pose.position.z,
    //           msg.roll, msg.pitch, msg.yaw);

    std::lock_guard<std::mutex> lock(qrcode_loc_mtx_);
    qrcode_loc_result_.timestamp_ms = msg.timestamp_ms;
    qrcode_loc_result_.mark_perception_status = msg.mark_perception_status;
    qrcode_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    qrcode_loc_result_.detect_status = static_cast<QRCodeDetectStatus>(msg.status);
    for (size_t i = 0; i < msg.qrcode_dis.size(); i++)
    {
        std::pair<int, float> pair{msg.qrcode_dis[i].id, msg.qrcode_dis[i].distance};
        qrcode_loc_result_.v_markID_dis.push_back(pair);
    }
    qrcode_loc_result_.markID = msg.mark_id;
    qrcode_loc_result_.target_direction = msg.target_direction;
    qrcode_loc_result_.xyzrpw.x = msg.pose.position.x;
    qrcode_loc_result_.xyzrpw.y = msg.pose.position.y;
    qrcode_loc_result_.xyzrpw.z = msg.pose.position.z;
    qrcode_loc_result_.xyzrpw.r = msg.roll;
    qrcode_loc_result_.xyzrpw.p = msg.pitch;
    qrcode_loc_result_.xyzrpw.w = msg.yaw;
    if (mower_alg_)
    {
        mower_alg_->SetQRCodeLocationResult(qrcode_loc_result_);
    }
}

void NavigationMowerNode::DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg)
{
    std::lock_guard<std::mutex> lck(mark_loc_mtx_);
    mark_loc_result_.timestamp = msg.timestamp_ms;
    mark_loc_result_.mark_perception_status = msg.mark_perception_status;
    mark_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    mark_loc_result_.detect_status = static_cast<int>(msg.detect_status);
    mark_loc_result_.roi_confidence = msg.roi_confidence;
    mark_loc_result_.target_direction = msg.target_direction;
    mark_loc_result_.mark_id = msg.mark_id;
    mark_loc_result_.xyzrpw.x = msg.pose.position.x;
    mark_loc_result_.xyzrpw.y = msg.pose.position.y;
    mark_loc_result_.xyzrpw.z = msg.pose.position.z;
    mark_loc_result_.xyzrpw.r = msg.roll;
    mark_loc_result_.xyzrpw.p = msg.pitch;
    mark_loc_result_.xyzrpw.w = msg.yaw;
    mark_loc_result_.mark_id_distance.clear();
    for (int i = 0; i < msg.mark_id_dis.size(); i++)
    {
        MarkIdDistance mark_id_dis;
        mark_id_dis.mark_id = msg.mark_id_dis[i].id;
        mark_id_dis.distance = msg.mark_id_dis[i].distance;
        mark_loc_result_.mark_id_distance.push_back(mark_id_dis);
    }
}

void NavigationMowerNode::DealRandomMowerState(const fescue_msgs__msg__RandomMowerStateData &msg)
{
    // if (mower_alg_)
    // {
    //     mower_alg_->SetRandomMowerRunningState(static_cast<RandomMowerRunningState>(msg.state));
    // }
}

void NavigationMowerNode::CheckMCUExceptionTimeout()
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_mcu_exception_time_).count();
    if (duration > 100)
    {
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        LOG_WARN_THROTTLE(3000, "[MowerThread] MCUException 数据超时为 {} ms，状态恢复为 NORMAL", duration);
    }
}

void NavigationMowerNode::CheckBehaviorStateTimeout()
{
    std::lock_guard<std::mutex> lock(behavior_mtx_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_behavior_timeout_time_).count();
    if (duration > 100)
    {
        behavior_state_ = BehaviorRunningState::UNDEFINED;
        LOG_WARN_THROTTLE(3000, "[MowerThread] behavior_state_ 数据超时超过为 {} ms，状态重置为 UNDEFINED", duration);
    }
}

void NavigationMowerNode::CheckTimeouts()
{
    CheckMCUExceptionTimeout();
    CheckBehaviorStateTimeout();
}

void NavigationMowerNode::MowerThread()
{
    MowerAlgResult mower_result;
    MarkLocationResult mark_loc_result;
    QRCodeLocationResult qrcode_loc_result;
    CrossRegionRunningState cross_region_state;
    PerceptionFusionResult fusion_result;
    RechargeRunningState recharge_state;
    McuExceptionStatus mcu_exception_status;
    BehaviorRunningState behavior_state;

    while (thread_running_.load())
    {
        if (mower_enable_.load())
        {
            CheckTimeouts();
            cross_region_state = cross_region_state_.load();
            recharge_state = recharge_state_.load();
            {
                std::scoped_lock lock(fusion_mutex_, mark_loc_mtx_, qrcode_loc_mtx_, mcu_exception_mutex_, behavior_mtx_);
                fusion_result = fusion_result_;
                mark_loc_result = mark_loc_result_;
                qrcode_loc_result = qrcode_loc_result_;
                mcu_exception_status = mcu_exception_status_;
                behavior_state = behavior_state_;
            }
            if (mower_alg_)
            {
                mower_result = mower_alg_->Run(mark_loc_result, cross_region_state,
                                               qrcode_loc_result, fusion_result, recharge_state,
                                               mcu_exception_status, behavior_state);
                if (mower_result.mower_completed)
                {
                    mower_alg_->ResetMowerAlgFlags();
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationMowerAlg disable!");
            if (mower_alg_)
            {
                mower_alg_->ResetMowerAlgFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationMowerNode::DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data)
{
    if (!data.empty() && pub_nav_alg_ctrl_)
    {
        size_t data_size = data.size() > MAX_NAVIGATION_ALGO_NUM ? MAX_NAVIGATION_ALGO_NUM : data.size();
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        nav_alg_ctrl_data.sender.unsafe_assign("NavigationMowerNode");
        for (size_t i = 0; i < data_size; i++)
        {
            fescue_msgs__msg__NavigationAlgoCtrlInfo info;
            info.type = static_cast<fescue_msgs__enum__NavigationAlgoType>(data.at(i).alg_id);
            info.state = static_cast<fescue_msgs__enum__NavigationAlgoState>(data.at(i).alg_status);
            nav_alg_ctrl_data.data.push_back(info);
        }
        pub_nav_alg_ctrl_->publishCopyOf(nav_alg_ctrl_data).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode nav_alg_ctrl_data Unable to publishCopyOf, error: " << error << std::endl;
        });
    }
}

bool NavigationMowerNode::DealMarkLocationMarkIdCallback(int mark_id)
{
    auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetDetectMarkId_Request,
                                                      fescue_msgs__srv__SetDetectMarkId_Response>>("mark_location_set_detect_mark_id");

    auto request_handler = [](const fescue_msgs__srv__SetDetectMarkId_Request &request_input,
                              fescue_msgs__srv__SetDetectMarkId_Request &request_send) -> void {
        request_send.data.mark_id = request_input.data.mark_id;
    };

    auto response_handler = [](const fescue_msgs__srv__SetDetectMarkId_Response &response_receive,
                               fescue_msgs__srv__SetDetectMarkId_Response &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    fescue_msgs__srv__SetDetectMarkId_Request request_input;
    fescue_msgs__srv__SetDetectMarkId_Response response_output;
    request_input.data.mark_id = mark_id;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendCrossRegionFinalResult(bool cross_region_result)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CrossRegionResultRequest,
                                                      mower_msgs::srv::CrossRegionResultResponse>>("cross_region_result");

    auto request_handler = [](const mower_msgs::srv::CrossRegionResultRequest &request_input,
                              mower_msgs::srv::CrossRegionResultRequest &request_send) {
        request_send.cross_region_result_type = request_input.cross_region_result_type;
    };

    auto response_handler = [](const mower_msgs::srv::CrossRegionResultResponse &response_receive,
                               mower_msgs::srv::CrossRegionResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CrossRegionResultRequest request_input;
    mower_msgs::srv::CrossRegionResultResponse response_output;
    request_input.cross_region_result_type = cross_region_result ? mower_msgs::srv::CrossRegionResultType::SUCCESS
                                                                 : mower_msgs::srv::CrossRegionResultType::FAIL;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendRechargeFinalResult(bool recharge_completed, bool recharge_result, mower_msgs::srv::RechargeOperationStatus status)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::RechargeResultRequest,
                                                      mower_msgs::srv::RechargeResultResponse>>("recharge_result");
    auto request_handler = [](const mower_msgs::srv::RechargeResultRequest &request_input,
                              mower_msgs::srv::RechargeResultRequest &request_send) {
        request_send.timestamp_ms = request_input.timestamp_ms;
        request_send.completed = request_input.completed;
        request_send.result = request_input.result;
        request_send.operation_status = request_input.operation_status;
    };

    auto response_handler = [](const mower_msgs::srv::RechargeResultResponse &response_receive,
                               mower_msgs::srv::RechargeResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::RechargeResultRequest request_input;
    mower_msgs::srv::RechargeResultResponse response_output;
    request_input.timestamp_ms = GetTimestampMs();
    request_input.completed = recharge_completed;
    request_input.result = recharge_result;
    request_input.operation_status = status;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendUndockFinalResult(bool undock_completed, bool undock_result, mower_msgs::srv::UndockOperationStatus status)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::UndockResultRequest,
                                                      mower_msgs::srv::UndockResultResponse>>("undock_result");
    auto request_handler = [](const mower_msgs::srv::UndockResultRequest &request_input,
                              mower_msgs::srv::UndockResultRequest &request_send) {
        request_send = request_input;
    };

    auto response_handler = [](const mower_msgs::srv::UndockResultResponse &response_receive,
                               mower_msgs::srv::UndockResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::UndockResultRequest request_input;
    mower_msgs::srv::UndockResultResponse response_output;
    request_input.timestamp_ms = GetTimestampMs();
    request_input.completed = undock_completed;
    request_input.result = undock_result;
    request_input.operation_status = status;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendSpiralMowerFinalResult(bool spiral_completed, bool spiral_result)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::SpiralMowResultRequest,
                                                      mower_msgs::srv::SpiralMowResultResponse>>("spiral_mow_result");

    auto request_handler = [](const mower_msgs::srv::SpiralMowResultRequest &request_input,
                              mower_msgs::srv::SpiralMowResultRequest &request_send) {
        request_send.timestamp_ms = request_input.timestamp_ms;
        request_send.result = request_input.result;
    };

    auto response_handler = [](const mower_msgs::srv::SpiralMowResultResponse &response_receive,
                               mower_msgs::srv::SpiralMowResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::SpiralMowResultRequest request_input;
    mower_msgs::srv::SpiralMowResultResponse response_output;
    request_input.timestamp_ms = GetTimestampMs();
    request_input.result = spiral_result;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendRegionExporeFinalResult(const RegionExploreResult &result)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::ExploreMapResultRequest,
                                                      mower_msgs::srv::ExploreMapResultResponse>>("explore_map_result");

    auto request_handler = [](const mower_msgs::srv::ExploreMapResultRequest &request_input,
                              mower_msgs::srv::ExploreMapResultRequest &request_send) {
        request_send = request_input;
    };

    auto response_handler = [](const mower_msgs::srv::ExploreMapResultResponse &response_receive,
                               mower_msgs::srv::ExploreMapResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::ExploreMapResultRequest request_input;
    mower_msgs::srv::ExploreMapResultResponse response_output;
    request_input.timestamp = result.timestamp;
    request_input.result = result.result;
    request_input.master_region_map_result = result.master_region_map_result;
    request_input.slave_region_map_result = result.slave_region_map_result;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::SendCutBorderFinalResult(bool result)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CutEdgeResultRequest,
                                                      mower_msgs::srv::CutEdgeResultResponse>>("cut_edge_result");
    auto request_handler = [](const mower_msgs::srv::CutEdgeResultRequest &request_input,
                              mower_msgs::srv::CutEdgeResultRequest &request_send) {
        request_send = request_input;
    };

    auto response_handler = [](const mower_msgs::srv::CutEdgeResultResponse &response_receive,
                               mower_msgs::srv::CutEdgeResultResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CutEdgeResultRequest request_input;
    mower_msgs::srv::CutEdgeResultResponse response_output;
    request_input.timestamp = GetTimestampMs();
    request_input.result = result;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::DealAreaCalcStart(uint64_t timestamp_ms)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::AreaCalculationStartRequest,
                                                      mower_msgs::srv::AreaCalculationStartResponse>>("localization_area_calculation_start");
    auto request_handler = [](const mower_msgs::srv::AreaCalculationStartRequest &request_input,
                              mower_msgs::srv::AreaCalculationStartRequest &request_send) {
        request_send.timestamp = request_input.timestamp;
    };

    auto response_handler = [](const mower_msgs::srv::AreaCalculationStartResponse &response_receive,
                               mower_msgs::srv::AreaCalculationStartResponse &response_output) -> bool {
        response_output.success = response_receive.success;
        response_output.timestamp = response_receive.timestamp;
        return response_output.success;
    };

    mower_msgs::srv::AreaCalculationStartRequest request_input;
    mower_msgs::srv::AreaCalculationStartResponse response_output;
    request_input.timestamp = timestamp_ms;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

bool NavigationMowerNode::DealAreaCalcStop(uint64_t timestamp_ms, float &area, float &perimeter)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::AreaCalculationStopRequest,
                                                      mower_msgs::srv::AreaCalculationStopResponse>>("localization_area_calculation_stop");
    auto request_handler = [](const mower_msgs::srv::AreaCalculationStopRequest &request_input,
                              mower_msgs::srv::AreaCalculationStopRequest &request_send) {
        request_send.timestamp = request_input.timestamp;
    };

    auto response_handler = [](const mower_msgs::srv::AreaCalculationStopResponse &response_receive,
                               mower_msgs::srv::AreaCalculationStopResponse &response_output) -> bool {
        memcpy(&response_output, &response_receive, sizeof(response_output));
        return response_output.success;
    };

    mower_msgs::srv::AreaCalculationStopRequest request_input;
    mower_msgs::srv::AreaCalculationStopResponse response_output;
    request_input.timestamp = timestamp_ms;
    if (client->SendRequest(request_input, response_output, request_handler, response_handler))
    {
        area = response_output.result.area;
        perimeter = response_output.result.perimeter;
        LOG_INFO("send area calc stop request success, area {:.2f} m^2, perimeter {:.2f} m", area, perimeter);
        return true;
    }
    else
    {
        LOG_ERROR("send area calc stop request failed");
        return false;
    }
}

void NavigationMowerNode::DealUndockFinalResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status)
{
    LOG_WARN("NavigationMowerNode undock status: {}, completed: {}, result: {}", static_cast<int>(status), completed, result);
    SendUndockFinalResult(completed, result, status);
}

bool NavigationMowerNode::DealRegionExploreAreaCalcStart(uint64_t timestamp)
{
    LOG_WARN("NavigationMowerNode region explore area calc start, timestamp: {}", timestamp);
    return DealAreaCalcStart(timestamp);
}

bool NavigationMowerNode::DealRegionExploreAreaCalcStop(uint64_t timestamp, float &area, float &perimeter)
{
    LOG_WARN("NavigationMowerNode region explore area calc stop, timestamp: {}", timestamp);
    return DealAreaCalcStop(timestamp, area, perimeter);
}

void NavigationMowerNode::DealRegionExploreResult(RegionExploreResult &result)
{
    LOG_WARN("NavigationMowerNode region explore completed, result: {}", result.result);
    SetMowerAppTriggersRegionExplore(false);
    SendRegionExporeFinalResult(result);
}

void NavigationMowerNode::DealCutBorderResult(bool completed, bool result)
{
    LOG_WARN("NavigationMowerNode cut border completed, result: {}", result);
    SetMowerAppTriggersCutBorder(false);
    SendCutBorderFinalResult(result);
}

void NavigationMowerNode::DealCrossRegionRunningStateCallback(CrossRegionRunningState state)
{
    if (pub_cross_region_state_)
    {
        fescue_msgs__msg__CrossRegionStateData data;
        data.state = static_cast<fescue_msgs_enum__CrossRegionState>(state);
        pub_cross_region_state_->publishCopyOf(data).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode pub_cross_region_state_ Unable to publishCopyOf, error: "
                      << error << std::endl;
        });
    }
}

void NavigationMowerNode::DealRechargeRunningStateCallback(RechargeRunningState state)
{
    if (pub_recharge_state_)
    {
        fescue_msgs__msg__RechargeStateData data;
        data.state = static_cast<fescue_msgs_enum__RechargeState>(state);
        pub_recharge_state_->publishCopyOf(data).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode pub_recharge_state_ Unable to publishCopyOf, error: "
                      << error << std::endl;
        });
    }
}

bool NavigationMowerNode::DealSWStandByRequest(const mower_msgs::srv::GoToStandbyRequestType &data)
{
    if (data == mower_msgs::srv::GoToStandbyRequestType::STANDBY)
    {
        LOG_WARN("Receive SW STANDBY request, stop all task!");
        is_recharge_start_ = false;
        is_cross_region_start_ = false;
        is_random_mower_start_ = false;
        is_region_explore_start_ = false;
        is_spiral_mower_start_ = false;
        is_cut_border_start_ = false;
        CloseAllTask();
        SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::DISABLE);
    }

    return true;
}

bool NavigationMowerNode::DealSWMowerRequest(const mower_msgs::srv::GoMowRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED)
        {
            LOG_WARN("Receive SW RANDOM_MOW request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW RANDOM_MOW request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state == RechargeRunningState::EDGE_FINDING_QR_CODE ||
             recharge_state == RechargeRunningState::UNDEFINED) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED))
        {
            LOG_WARN("Receive SW RANDOM_MOW request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW RANDOM_MOW request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoMowRequestType::RANDOM_MOW)
    {
        LOG_WARN("Receive SW RANDOM_MOW request, run RANDOM_MOW!");
        SetPerceptionAndLocalizationStateOnRandomMower();
        SetMowerAppTriggersMower(true);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoMowRequestType::SPIRAL_MOW)
    {
        LOG_WARN("Receive SW SPIRAL_MOW request, run SPIRAL_MOW!");
        SetPerceptionAndLocalizationStateOnSpiralMower();
        SetMowerAppTriggersSpiralMower(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoMowRequestType::PAUSE)
    {
        LOG_WARN("Receive SW MOWER PAUSE request, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersMower(false);
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWMowerRequest, Invalid request type {}!", static_cast<int>(data));
    }

    return true;
}

bool NavigationMowerNode::DealSWChargeRequest(const mower_msgs::srv::GoChargeRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED)
        {
            LOG_WARN("Receive SW GO_CHARGE request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW GO_CHARGE request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoChargeRequestType::GO_TO_CHARGE)
    {
        LOG_WARN("Receive SW GO_TO_CHARGE request, run go charge!");
        SetPerceptionAndLocalizationStateOnRecharge();
        SetMowerAppTriggersRecharge(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoChargeRequestType::PAUSE)
    {
        LOG_WARN("Receive SW GO_TO_CHARGE PAUSE request, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }

    return true;
}

bool NavigationMowerNode::DealSWGoCrossRegionRequest(const mower_msgs::srv::GoToCrossRegionRequestType &data)
{
    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if (recharge_state == RechargeRunningState::EDGE_FINDING_QR_CODE ||
            recharge_state == RechargeRunningState::UNDEFINED)
        {
            LOG_WARN("Receive SW CROS_REGION request, recharge state {}, stop it!", asStringLiteral(recharge_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW CROS_REGION request, recharge state {}, ignore it!", asStringLiteral(recharge_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoToCrossRegionRequestType::GO_TO_CROSS_REGION)
    {
        LOG_WARN("Receive SW GO_TO_CROSS_REGION request, run cross region!");
        SetPerceptionAndLocalizationStateOnCrossRegion();
        SetMowerAppTriggersCrossRegion(true);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoToCrossRegionRequestType::PAUSE)
    {
        LOG_WARN("Receive SW CROSS_REGION PAUSE request, cross region running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWGoCrossRegionRequest, Invalid request type {}!", static_cast<int>(data));
    }

    return true;
}

bool NavigationMowerNode::DealSWRegionExploreRequest(const mower_msgs::srv::ExploreMapRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED)
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state == RechargeRunningState::EDGE_FINDING_QR_CODE ||
             recharge_state == RechargeRunningState::UNDEFINED) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED))
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::ExploreMapRequestType::EXPLORE_MAP)
    {
        LOG_WARN("Receive SW EXPLORE_MAP request, run explore map!");
        SetPerceptionAndLocalizationStateOnExploreMap();
        SetMowerAppTriggersRegionExplore(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::ExploreMapRequestType::PAUSE)
    {
        LOG_WARN("Receive SW EXPLORE_MAP PAUSE request, explore map running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        return false;
    }

    return true;
}

bool NavigationMowerNode::DealSWCutBorderRequest(const mower_msgs::srv::GoToCutEdgeRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED)
        {
            LOG_WARN("Receive SW CUT_BORDER request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW CUT_BORDER request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state == RechargeRunningState::EDGE_FINDING_QR_CODE ||
             recharge_state == RechargeRunningState::UNDEFINED) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED))
        {
            LOG_WARN("Receive SW CUT_BORDER request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW CUT_BORDER request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoToCutEdgeRequestType::GO_TO_CUT_EDGE)
    {
        LOG_WARN("Receive SW GO_TO_CUT_EDGE request, run cut edge!");
        SetPerceptionAndLocalizationStateOnCutBorder();
        SetMowerAppTriggersCutBorder(true);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoToCutEdgeRequestType::PAUSE)
    {
        LOG_WARN("Receive SW CUT_BORDER PAUSE request, cut edge running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        return false;
    }

    return true;
}

bool NavigationMowerNode::SetMowerNodeParam(const fescue_msgs__msg__NavigationMowerNodeParam &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();

    NavigationMowerNodeConfig config = Config<NavigationMowerNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<NavigationMowerNodeConfig>::SetConfig(config);
    LOG_INFO("New NavigationMowerNode params: {}", config.toString().c_str());
    return true;
}

bool NavigationMowerNode::GetMowerNodeParam(fescue_msgs__msg__NavigationMowerNodeParam &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

void NavigationMowerNode::PublishRechargeAlgCtrl(const std::string &sender, bool recharge_enable)
{
    if (pub_nav_alg_ctrl_)
    {
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        nav_alg_ctrl_data.sender.unsafe_assign(sender.c_str());
        fescue_msgs__msg__NavigationAlgoCtrlInfo recharge_info;
        recharge_info.type = FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RECHARGE;
        recharge_info.state = (recharge_enable ? FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE : FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE);
        nav_alg_ctrl_data.data.push_back(recharge_info);
        pub_nav_alg_ctrl_->publishCopyOf(nav_alg_ctrl_data).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode PublishRechargeAlgCtrl Unable to publishCopyOf, error: " << error << std::endl;
        });
    }
}

void NavigationMowerNode::VelociyAdjustment(float motor_speed_left, float motor_speed_right,
                                            float expected_linear, float expected_angular,
                                            float &actual_linear, float &actual_angular,
                                            float &adjusted_linear, float &adjusted_angular)
{
    // 计算实际线速度和角速度
    GetVelocityFromMotorRPM(motor_speed_left, motor_speed_right, wheel_radius_, wheel_base_, actual_linear, actual_angular);

    float adjustment_linear = adjusted_vel_kp_ * (expected_linear - actual_linear);
    float adjustment_angular = adjusted_vel_kp_ * (expected_angular - actual_angular);
    adjusted_linear = expected_linear + adjustment_linear;
    adjusted_angular = expected_angular + adjustment_angular;

    // 增加线速度和角速度的限制
    if ((adjusted_linear >= 0 && adjusted_linear > vel_limit_param_ * expected_linear) ||
        (adjusted_linear < 0 && adjusted_linear < vel_limit_param_ * expected_linear))
    {
        adjusted_linear = vel_limit_param_ * expected_linear;
    }
    if ((adjusted_angular >= 0 && adjusted_angular > vel_limit_param_ * expected_angular) ||
        (adjusted_angular < 0 && adjusted_angular < vel_limit_param_ * expected_angular))
    {
        adjusted_angular = vel_limit_param_ * expected_angular;
    }
}

void NavigationMowerNode::PublishMowerTwist(const std::string &sender, float linear, float angular)
{
    mower_msgs::msg::Twist twist;
    if (mower_state_ != MowerRunningState::RUNNING)
    {
        twist.linear_velocity = 0.0;
        twist.angular_velocity = 0.0;
    }
    else
    {
        if (is_vel_adjustment_)
        {
            MotorSpeedData motor_speed_data;
            {
                std::lock_guard<std::mutex> lock(motor_speed_mtx_);
                motor_speed_data = motor_speed_data_;
            }
            float adjusted_linear{0.0};
            float adjusted_angular{0.0};
            float actual_linear{0.0};
            float actual_angular{0.0};
            VelociyAdjustment(motor_speed_data.motor_speed_left, motor_speed_data.motor_speed_right,
                              linear, angular, actual_linear, actual_angular, adjusted_linear, adjusted_angular);
            LOG_WARN_THROTTLE(1000, "NavigationMowerNode velocity actual:({:.2f} {:.2f}), expected:({:.2f} {:.2f}), adjusted:({:.2f} {:.2f})",
                              actual_linear, actual_angular, linear, angular, adjusted_linear, adjusted_angular);
            twist.linear_velocity = adjusted_linear;
            twist.angular_velocity = adjusted_angular;
        }
        else
        {
            twist.linear_velocity = linear;
            twist.angular_velocity = angular;
        }
    }
    LOG_WARN_THROTTLE(1000, "NavigationMowerNode publish [{}] MowerRunningState {} twist {:.2f} {:.2f}",
                      sender.c_str(), asStringLiteral(mower_state_), twist.linear_velocity, twist.angular_velocity);
    PublishMCUTwist(twist);
}

void NavigationMowerNode::PublishRunningState(MowerRunningState state)
{
    if (pub_nav_running_state_)
    {
        fescue_msgs__msg__NavigationRunningStateData running_state;
        running_state.sender.unsafe_assign("NavigationMowerNode");
        running_state.state = static_cast<fescue_msgs__enum__NavigationRunningState>(state);
        LOG_WARN("NavigationMowerNode publish MowerRunningState {}", static_cast<int>(state));
        pub_nav_running_state_->publishCopyOf(running_state).or_else([](auto &error) {
            std::cerr << "NavigationMowerNode  PublishRunningState Unable to publishCopyOf, error: " << error << std::endl;
        });
    }
}

void NavigationMowerNode::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (mower_alg_)
    {
        mower_alg_->PublishException(level, value);
    }
}

void NavigationMowerNode::AlgorithmParamToConfigParam(const MowerAlgParam &param, NavigationMowerAlgConfig &config)
{
    config.is_enable_unstake_mode = param.is_enable_unstake_mode;
    config.unstake_distance = param.unstake_distance;
    config.unstake_adjust_yaw = param.unstake_adjust_yaw;
    config.unstake_vel_linear = param.unstake_vel_linear;
    config.unstake_vel_angular = param.unstake_vel_angular;

    config.mower_linear = param.mower_linear;
    config.mower_angular = param.mower_angular;
    // config.perception_drive_cooldown_time = param.perception_drive_cooldown_time;
    config.edge_mode_direction = param.edge_mode_direction;
    config.cross_region_adjust_yaw = param.cross_region_adjust_yaw;
    config.cross_region_adjust_displace = param.cross_region_adjust_displace;
    config.mark_distance_threshold = param.mark_distance_threshold;

    config.camera_2_center_dis = param.camera_2_center_dis;

    config.edge_perception_drive_cooldown_time_threshold = param.edge_perception_drive_cooldown_time_threshold;
    config.qr_detection_cooldown_time_threshold = param.qr_detection_cooldown_time_threshold;
    config.mark_detection_cooldown_time_threshold = param.mark_detection_cooldown_time_threshold;
}

void NavigationMowerNode::ConfigParamToAlgorithmParam(const NavigationMowerAlgConfig &config, MowerAlgParam &param)
{
    param.is_enable_unstake_mode = config.is_enable_unstake_mode;
    param.unstake_distance = config.unstake_distance;
    param.unstake_adjust_yaw = config.unstake_adjust_yaw;
    param.unstake_vel_linear = config.unstake_vel_linear;
    param.unstake_vel_angular = config.unstake_vel_angular;

    param.mower_linear = config.mower_linear;
    param.mower_angular = config.mower_angular;
    // param.perception_drive_cooldown_time = config.perception_drive_cooldown_time;
    param.edge_mode_direction = config.edge_mode_direction;
    param.cross_region_adjust_yaw = config.cross_region_adjust_yaw;
    param.cross_region_adjust_displace = config.cross_region_adjust_displace;
    param.mark_distance_threshold = config.mark_distance_threshold;

    param.camera_2_center_dis = config.camera_2_center_dis;

    param.edge_perception_drive_cooldown_time_threshold = config.edge_perception_drive_cooldown_time_threshold;
    param.qr_detection_cooldown_time_threshold = config.qr_detection_cooldown_time_threshold;
    param.mark_detection_cooldown_time_threshold = config.mark_detection_cooldown_time_threshold;
}

void NavigationMowerNode::CloseAllTask()
{
    if (mower_alg_)
    {
        mower_alg_->SetAllTaskClose();
    }
}

void NavigationMowerNode::DealRechargeFinalResult(const fescue_msgs__msg__NavRechargeFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode recharge completed, result: {}", msg.result);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCutBorder(false);
        SetMowerComplete(true);
        CloseAllTask();
        SendRechargeFinalResult(msg.completed, msg.result); // 通知业务层，回充已经完成
    }
}

void NavigationMowerNode::DealCrossRegionFinalResult(const fescue_msgs__msg__NavCrossRegionFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode cross region completed, result: {}", msg.result);
        SetMowerAppTriggersCrossRegion(false);
        SendCrossRegionFinalResult(msg.result); // 通知业务层，跨区域已经完成
    }
}

void NavigationMowerNode::DealSpiralMowerFinalResult(const ob_mower_msgs::NavSpiralMowerFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode spiral mower completed, result: {}", msg.success);
        SetMowerAppTriggersSpiralMower(false); // 结束螺旋割草
        SendSpiralMowerFinalResult(true, msg.success);
    }
}

void NavigationMowerNode::PublishPerceptionLocalizationAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &msg)
{
    if (pub_perception_localization_alg_ctrl_)
    {
        pub_perception_localization_alg_ctrl_->publish(msg);
    }
}

void NavigationMowerNode::PublishMCUTwist(const mower_msgs::msg::Twist &msg)
{
    if (pub_nav_twist_)
    {
        pub_nav_twist_->publishCopyOf(msg);
    }
}

void NavigationMowerNode::SetLocalizationAreaEstimateState(ob_mower_msgs::PerceptionLocalizationAlgState state)
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, state));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState state)
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, state));

    // area estimation is always enable
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION,
                                                                  PerceptionLocalizationAlgState::ENABLE));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRecharge()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRechargeHaveFindQRCode()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRechargeFindingQRCode()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRandomMower()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;

    ctrl.sender = "navigation_mower_node";
    // enable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));

    // disable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, disable));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnCrossRegion()
{
    // close charge station detection, station localization;
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;

    ctrl.sender = "navigation_mower_node";
    // enable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CROSS_REGION_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    // close algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, disable));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnExploreMap()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnCutBorder()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnSpiralMower()
{
    SetPerceptionAndLocalizationStateOnRandomMower();
}

bool NavigationMowerNode::GetMCUMissionType(mower_msgs::srv::MowerMissionType &mission_type)
{
    auto client = std::make_unique<IceoryxClientMower<get_mcu_mission_type_request, get_mcu_mission_type_response>>("get_mcu_mission_type");
    auto request_handler = [](const get_mcu_mission_type_request &request_input, get_mcu_mission_type_request &request_send) -> void {
        request_send = request_input;
    };

    auto response_handler = [](const get_mcu_mission_type_response &response_receive, get_mcu_mission_type_response &response_output) -> bool {
        response_output = response_receive;
        return response_output.success;
    };

    get_mcu_mission_type_request request_input;
    request_input.timestamp = GetTimestampMs();
    get_mcu_mission_type_response response_output;
    if (!client->SendRequest(request_input, response_output, request_handler, response_handler))
    {
        LOG_ERROR("NavigationMowerNode get mcu mission type failed!");
        return false;
    }

    mission_type = response_output.mcu_mission_type;
    return true;
}

void NavigationMowerNode::UpdateStuckDetectionState(float linear_velocity, float angular_velocity)
{
    // 获取编码器实际速度
    float actual_linear_velocity = 0.0f;
    float actual_angular_velocity = 0.0f;

    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        // 使用nav_common中的函数计算实际速度
        GetVelocityFromMotorRPM(motor_speed_data_.motor_speed_left,
                                motor_speed_data_.motor_speed_right,
                                wheel_radius_, wheel_base_,
                                actual_linear_velocity, actual_angular_velocity);
    }

    // 判断理论速度是否有运动指令
    bool theoretical_has_motion = (std::abs(linear_velocity) > 0.01f) || (std::abs(angular_velocity) > 0.01f);

    // 判断编码器实际速度是否有运动
    bool actual_has_motion = (std::abs(actual_linear_velocity) > 0.01f) || (std::abs(actual_angular_velocity) > 0.01f);

    // 脱困检测应该激活的条件：理论速度和实际速度都大于阈值
    bool should_be_active = theoretical_has_motion && actual_has_motion;

    auto now = std::chrono::steady_clock::now();

    // 如果期望状态发生变化，重置计时器
    if (should_be_active != stuck_detection_should_be_active_)
    {
        stuck_detection_should_be_active_ = should_be_active;
        stuck_detection_state_change_time_ = now;

        LOG_INFO("[NavigationMowerNode] 脱困检测期望状态变化为: {}, 重置计时器 - 理论速度({:.3f},{:.3f}), 实际速度({:.3f},{:.3f})",
                 should_be_active ? "启动" : "暂停",
                 linear_velocity, angular_velocity,
                 actual_linear_velocity, actual_angular_velocity);
        return;
    }

    // 计算状态持续时间
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - stuck_detection_state_change_time_).count() / 1000.0f;

    // 根据期望状态和持续时间决定是否切换实际状态
    if (should_be_active && !stuck_detection_current_active_)
    {
        // 期望启动，当前未启动，检查是否达到启动阈值
        if (duration >= stuck_detection_start_threshold_seconds_)
        {
            stuck_detection_current_active_ = true;
            if (mower_alg_)
            {
                mower_alg_->SetStuckDetectionActive(true);
            }
            LOG_INFO("[NavigationMowerNode] 运动指令持续{:.1f}秒，启动脱困检测系统 - 理论速度({:.3f},{:.3f}), 实际速度({:.3f},{:.3f})",
                     duration, linear_velocity, angular_velocity, actual_linear_velocity, actual_angular_velocity);
        }
        else
        {
            LOG_INFO("[NavigationMowerNode] 运动指令持续{:.1f}秒，等待达到启动脱困阈值{:.1f}秒 - 理论速度({:.3f},{:.3f}), 实际速度({:.3f},{:.3f})",
                     duration, stuck_detection_start_threshold_seconds_,
                     linear_velocity, angular_velocity, actual_linear_velocity, actual_angular_velocity);
        }
    }
    else if (!should_be_active && stuck_detection_current_active_)
    {
        // 期望暂停，当前已启动，检查是否达到暂停阈值
        if (duration >= stuck_detection_stop_threshold_seconds_)
        {
            stuck_detection_current_active_ = false;
            if (mower_alg_)
            {
                mower_alg_->SetStuckDetectionActive(false);
            }
            LOG_INFO("[NavigationMowerNode] 机器人静止持续{:.1f}秒，暂停脱困检测系统 - 理论速度({:.3f},{:.3f}), 实际速度({:.3f},{:.3f})",
                     duration, linear_velocity, angular_velocity, actual_linear_velocity, actual_angular_velocity);
        }
        else
        {
            LOG_INFO("[NavigationMowerNode] 机器人静止持续{:.1f}秒，等待达到暂停脱困阈值{:.1f}秒 - 理论速度({:.3f},{:.3f}), 实际速度({:.3f},{:.3f})",
                     duration, stuck_detection_stop_threshold_seconds_,
                     linear_velocity, angular_velocity, actual_linear_velocity, actual_angular_velocity);
        }
    }
}

#if PRINTF_IMU_DATA
void NavigationMowerNode::OpenImuDataFile()
{
    std::string file_path = log_dir_ + "/imu_data.txt";
    imu_data_file_.open(file_path, std::ios::out);
    if (imu_data_file_.is_open())
    {
        LOG_INFO("IMU数据文件已打开: {}", file_path.c_str());
        // 写入标题行
        imu_data_file_ << "timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,filtered_accel_x,filtered_accel_y,filtered_accel_z,filtered_gyro_x,filtered_gyro_y,filtered_gyro_z" << std::endl;
    }
    else
    {
        LOG_ERROR("无法打开IMU数据文件: {}", file_path.c_str());
    }
}
#endif

} // namespace fescue_iox
