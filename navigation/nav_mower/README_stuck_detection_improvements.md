# 脱困检测系统改进说明

## 概述

本次改进主要包含三个方面：
1. 将SlidingWindow类分文件封装
2. 创建Python脚本用于分析角度累计量数据
3. 增加理论速度和编码器实际速度启停逻辑

## 1. SlidingWindow类分文件封装

### 修改内容
- 创建了新的头文件：`navigation/nav_mower/include/sliding_window.hpp`
- 创建了新的源文件：`navigation/nav_mower/src/sliding_window.cpp`
- 将SlidingWindow结构体和MovementData结构体从`stuck_detection_recovery.hpp`移动到新文件
- 将SlidingWindow的方法实现从`stuck_detection_recovery.cpp`移动到新文件
- 更新了`stuck_detection_recovery.hpp`以包含新的头文件

### 优势
- 代码结构更清晰，职责分离
- 便于单独测试和维护SlidingWindow功能
- 减少了stuck_detection_recovery文件的复杂度

## 2. Python脚本分析角度累计量

### 文件位置
- 主脚本：`scripts/analyze_stuck_detection.py`
- 使用示例：`scripts/run_stuck_analysis.sh`

### 功能特性
- 解析日志文件中的脱困检测数据
- 绘制5分钟、10分钟、15分钟窗口的角度累计量图表
- 显示阈值线进行对比分析
- 标记被困状态的时间点
- 提供统计信息（最小值、最大值、平均值、被困次数）

### 使用方法

#### 方法1：直接使用Python脚本
```bash
python3 scripts/analyze_stuck_detection.py /path/to/log/file.log -o output.png
```

#### 方法2：使用Shell脚本
```bash
# 使用默认日志文件路径
./scripts/run_stuck_analysis.sh

# 指定日志文件路径
./scripts/run_stuck_analysis.sh /path/to/your/log/file.log
```

### 日志格式要求
脚本能够解析以下格式的日志：
```
[2025-05-31 14:35:24.725] [navigation_mower_node] [warning] stuck_detection_recovery.cpp[697] [StuckDetectionRecovery] 5分钟窗口: 角度累计=67.210rad, 阈值=2.090rad, 被困=否, 数据=充足
```

## 3. 理论速度和编码器实际速度启停逻辑

### 修改内容
修改了`NavigationMowerNode::UpdateStuckDetectionState`函数，增加了以下功能：

#### 新增逻辑
1. **获取编码器实际速度**：
   - 使用`GetVelocityFromMotorRPM`函数从电机转速计算实际线速度和角速度
   - 考虑车轮半径和轴距参数

2. **双重速度检测**：
   - 理论速度检测：检查导航算法发出的速度指令
   - 实际速度检测：检查编码器反馈的实际运动速度
   - 只有当两者都大于阈值时，才启动脱困检测

3. **增强的日志输出**：
   - 同时显示理论速度和实际速度
   - 便于调试和分析速度差异

#### 启动条件
脱困检测系统启动需要满足：
- 理论线速度 > 0.01 m/s 或 理论角速度 > 0.01 rad/s
- **AND** 实际线速度 > 0.01 m/s 或 实际角速度 > 0.01 rad/s
- **AND** 上述条件持续时间 > 启动阈值（默认5秒）

#### 停止条件
脱困检测系统停止当：
- 理论速度 <= 0.01 或 实际速度 <= 0.01
- **AND** 上述条件持续时间 > 停止阈值（默认5秒）

### 优势
1. **更准确的检测**：避免在机器人实际没有运动时启动脱困检测
2. **防止误触发**：即使有速度指令，如果机器人实际没有移动（如卡住），也不会启动脱困检测
3. **更好的调试信息**：日志中包含理论和实际速度，便于分析问题

## 配置参数

### 脱困检测阈值
在`NavigationMowerNode`中可以配置以下参数：
- `stuck_detection_start_threshold_seconds_`：启动脱困检测需要持续的时间阈值（默认5.0秒）
- `stuck_detection_stop_threshold_seconds_`：暂停脱困检测需要持续的时间阈值（默认5.0秒）

### 速度阈值
- 理论速度阈值：0.01 m/s 或 0.01 rad/s
- 实际速度阈值：0.01 m/s 或 0.01 rad/s

## 测试建议

1. **功能测试**：
   - 测试机器人正常运动时脱困检测的启动和停止
   - 测试机器人卡住时脱困检测的行为
   - 测试机器人空转时脱困检测的行为

2. **性能测试**：
   - 观察脱困检测启动的延迟是否合适
   - 检查是否有误触发的情况

3. **日志分析**：
   - 使用Python脚本分析角度累计量数据
   - 检查理论速度和实际速度的差异

## 注意事项

1. **编译依赖**：确保项目能够找到`nav_common.hpp`头文件
2. **Python依赖**：分析脚本需要matplotlib库：`pip install matplotlib`
3. **日志权限**：确保脚本有权限读取日志文件
4. **参数调优**：根据实际测试结果调整速度阈值和时间阈值
